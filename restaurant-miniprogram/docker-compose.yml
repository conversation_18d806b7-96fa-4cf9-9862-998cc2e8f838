version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: restaurant_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: restaurant_miniprogram
      MYSQL_USER: restaurant_user
      MYSQL_PASSWORD: restaurant_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: restaurant_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Go后端API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restaurant_backend
    restart: always
    ports:
      - "3000:3000"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=restaurant_user
      - DB_PASSWORD=restaurant_password
      - DB_NAME=restaurant_miniprogram
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - GIN_MODE=release
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend/uploads:/root/uploads

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: restaurant_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./frontend:/usr/share/nginx/html
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend

volumes:
  mysql_data:
  redis_data:
