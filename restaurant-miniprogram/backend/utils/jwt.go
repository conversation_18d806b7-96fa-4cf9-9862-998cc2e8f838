package utils

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWT Claims结构
type Claims struct {
	UserID       uint   `json:"user_id"`
	UserType     string `json:"user_type"` // wechat_user, restaurant_admin, super_admin
	OpenID       string `json:"openid,omitempty"`
	RestaurantID uint   `json:"restaurant_id,omitempty"`
	jwt.RegisteredClaims
}

// 生成JWT token
func GenerateJWT(userID uint, userType string, secret string, expireHours int, extraData map[string]interface{}) (string, error) {
	claims := Claims{
		UserID:   userID,
		UserType: userType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 添加额外数据
	if extraData != nil {
		if openid, ok := extraData["openid"].(string); ok {
			claims.OpenID = openid
		}
		if restaurantID, ok := extraData["restaurant_id"].(uint); ok {
			claims.RestaurantID = restaurantID
		}
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

// 验证JWT token
func ValidateJWT(tokenString string, secret string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// 生成微信用户token
func GenerateWeChatUserToken(userID uint, openid string, secret string, expireHours int) (string, error) {
	extraData := map[string]interface{}{
		"openid": openid,
	}
	return GenerateJWT(userID, "wechat_user", secret, expireHours, extraData)
}

// 生成餐厅管理员token
func GenerateRestaurantAdminToken(adminID uint, restaurantID uint, secret string, expireHours int) (string, error) {
	extraData := map[string]interface{}{
		"restaurant_id": restaurantID,
	}
	return GenerateJWT(adminID, "restaurant_admin", secret, expireHours, extraData)
}

// 生成超级管理员token
func GenerateSuperAdminToken(adminID uint, secret string, expireHours int) (string, error) {
	return GenerateJWT(adminID, "super_admin", secret, expireHours, nil)
}
