package config

import (
	"os"
	"strconv"
)

type Config struct {
	// 服务器配置
	Port    string
	GinMode string

	// 数据库配置
	Database DatabaseConfig

	// Redis配置
	Redis RedisConfig

	// JWT配置
	JWT JWTConfig

	// 微信配置
	WeChat WeChatConfig

	// 微信支付配置
	WeChatPay WeChatPayConfig

	// OSS配置
	OSS OSSConfig

	// 系统配置
	System SystemConfig
}

type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	Name     string
	Charset  string
}

type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

type JWTConfig struct {
	Secret      string
	ExpireHours int
}

type WeChatConfig struct {
	AppID  string
	Secret string
}

type WeChatPayConfig struct {
	MchID    string
	Key      string
	CertPath string
	KeyPath  string
}

type OSSConfig struct {
	Endpoint        string
	AccessKeyID     string
	AccessKeySecret string
	BucketName      string
}

type SystemConfig struct {
	UploadMaxSize           int64
	ReferralCommissionRate  float64
}

func Load() *Config {
	redisDB, _ := strconv.Atoi(getEnv("REDIS_DB", "0"))
	jwtExpireHours, _ := strconv.Atoi(getEnv("JWT_EXPIRE_HOURS", "24"))
	uploadMaxSize, _ := strconv.ParseInt(getEnv("UPLOAD_MAX_SIZE", "10485760"), 10, 64)
	referralCommissionRate, _ := strconv.ParseFloat(getEnv("REFERRAL_COMMISSION_RATE", "0.05"), 64)

	return &Config{
		Port:    getEnv("PORT", "3000"),
		GinMode: getEnv("GIN_MODE", "debug"),
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "3306"),
			User:     getEnv("DB_USER", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Name:     getEnv("DB_NAME", "restaurant_miniprogram"),
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       redisDB,
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "default-secret"),
			ExpireHours: jwtExpireHours,
		},
		WeChat: WeChatConfig{
			AppID:  getEnv("WECHAT_APPID", ""),
			Secret: getEnv("WECHAT_SECRET", ""),
		},
		WeChatPay: WeChatPayConfig{
			MchID:    getEnv("WECHAT_PAY_MCHID", ""),
			Key:      getEnv("WECHAT_PAY_KEY", ""),
			CertPath: getEnv("WECHAT_PAY_CERT_PATH", ""),
			KeyPath:  getEnv("WECHAT_PAY_KEY_PATH", ""),
		},
		OSS: OSSConfig{
			Endpoint:        getEnv("OSS_ENDPOINT", ""),
			AccessKeyID:     getEnv("OSS_ACCESS_KEY_ID", ""),
			AccessKeySecret: getEnv("OSS_ACCESS_KEY_SECRET", ""),
			BucketName:      getEnv("OSS_BUCKET_NAME", ""),
		},
		System: SystemConfig{
			UploadMaxSize:          uploadMaxSize,
			ReferralCommissionRate: referralCommissionRate,
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
