package middleware

import (
	"net/http"
	"restaurant-miniprogram/config"
	"restaurant-miniprogram/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// 微信用户认证中间件
func WeChatAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "缺少认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token格式错误",
			})
			c.Abort()
			return
		}

		// 验证JWT token
		claims, err := utils.ValidateJWT(tokenString, cfg.JWT.Secret)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token无效或已过期",
			})
			c.Abort()
			return
		}

		// 检查用户类型
		if claims.UserType != "wechat_user" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "用户类型错误",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("user_type", claims.UserType)
		c.Set("openid", claims.OpenID)

		c.Next()
	}
}

// 餐厅管理员认证中间件
func AdminAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "缺少认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token格式错误",
			})
			c.Abort()
			return
		}

		// 验证JWT token
		claims, err := utils.ValidateJWT(tokenString, cfg.JWT.Secret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token无效或已过期",
			})
			c.Abort()
			return
		}

		// 检查用户类型
		if claims.UserType != "restaurant_admin" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "用户类型错误",
			})
			c.Abort()
			return
		}

		// 将管理员信息存储到上下文
		c.Set("admin_id", claims.UserID)
		c.Set("user_type", claims.UserType)
		c.Set("restaurant_id", claims.RestaurantID)

		c.Next()
	}
}

// 超级管理员认证中间件
func SuperAdminAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "缺少认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token格式错误",
			})
			c.Abort()
			return
		}

		// 验证JWT token
		claims, err := utils.ValidateJWT(tokenString, cfg.JWT.Secret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token无效或已过期",
			})
			c.Abort()
			return
		}

		// 检查用户类型
		if claims.UserType != "super_admin" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "用户类型错误",
			})
			c.Abort()
			return
		}

		// 将超级管理员信息存储到上下文
		c.Set("super_admin_id", claims.UserID)
		c.Set("user_type", claims.UserType)

		c.Next()
	}
}
