package routes

import (
	"restaurant-miniprogram/config"
	"restaurant-miniprogram/controllers"
	"restaurant-miniprogram/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(r *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// 初始化控制器
	authController := controllers.NewAuthController(db, cfg)
	restaurantController := controllers.NewRestaurantController(db, cfg)
	branchController := controllers.NewBranchController(db, cfg)
	categoryController := controllers.NewCategoryController(db, cfg)
	dishController := controllers.NewDishController(db, cfg)
	orderController := controllers.NewOrderController(db, cfg)
	userController := controllers.NewUserController(db, cfg)
	couponController := controllers.NewCouponController(db, cfg)
	paymentController := controllers.NewPaymentController(db, cfg)
	reviewController := controllers.NewReviewController(db, cfg)

	// API版本分组
	v1 := r.Group("/api/v1")

	// 微信小程序API
	miniprogram := v1.Group("/miniprogram")
	{
		// 用户认证
		miniprogram.POST("/auth/login", authController.WeChatLogin)
		miniprogram.POST("/auth/register", authController.WeChatRegister)

		// 需要登录的接口
		authorized := miniprogram.Group("")
		authorized.Use(middleware.WeChatAuth(cfg))
		{
			// 用户信息
			authorized.GET("/user/profile", userController.GetProfile)
			authorized.PUT("/user/profile", userController.UpdateProfile)
			authorized.GET("/user/orders", userController.GetOrders)
			authorized.GET("/user/coupons", userController.GetCoupons)
			authorized.GET("/user/referrals", userController.GetReferrals)

			// 餐厅和分店
			authorized.GET("/restaurants/:id", restaurantController.GetRestaurant)
			authorized.GET("/branches/:id", branchController.GetBranch)
			authorized.GET("/branches/:id/menu", branchController.GetMenu)

			// 菜品
			authorized.GET("/dishes/:id", dishController.GetDish)

			// 订单
			authorized.POST("/orders", orderController.CreateOrder)
			authorized.GET("/orders/:id", orderController.GetOrder)
			authorized.PUT("/orders/:id/cancel", orderController.CancelOrder)

			// 支付
			authorized.POST("/payments/wechat", paymentController.CreateWeChatPayment)
			authorized.POST("/payments/notify", paymentController.WeChatNotify)

			// 优惠券
			authorized.POST("/coupons/:id/claim", couponController.ClaimCoupon)

			// 评价
			authorized.POST("/reviews", reviewController.CreateReview)
			authorized.GET("/reviews", reviewController.GetReviews)
		}
	}

	// 餐厅管理后台API
	admin := v1.Group("/admin")
	{
		// 餐厅管理员认证
		admin.POST("/auth/login", authController.AdminLogin)

		// 需要管理员权限的接口
		authorized := admin.Group("")
		authorized.Use(middleware.AdminAuth(cfg))
		{
			// 餐厅信息
			authorized.GET("/restaurant", restaurantController.GetAdminRestaurant)
			authorized.PUT("/restaurant", restaurantController.UpdateRestaurant)

			// 分店管理
			authorized.GET("/branches", branchController.GetBranches)
			authorized.POST("/branches", branchController.CreateBranch)
			authorized.PUT("/branches/:id", branchController.UpdateBranch)
			authorized.DELETE("/branches/:id", branchController.DeleteBranch)
			authorized.POST("/branches/:id/qrcode", branchController.GenerateQRCode)

			// 菜品分类管理
			authorized.GET("/categories", categoryController.GetCategories)
			authorized.POST("/categories", categoryController.CreateCategory)
			authorized.PUT("/categories/:id", categoryController.UpdateCategory)
			authorized.DELETE("/categories/:id", categoryController.DeleteCategory)

			// 菜品管理
			authorized.GET("/dishes", dishController.GetDishes)
			authorized.POST("/dishes", dishController.CreateDish)
			authorized.PUT("/dishes/:id", dishController.UpdateDish)
			authorized.DELETE("/dishes/:id", dishController.DeleteDish)
			authorized.POST("/dishes/:id/specs", dishController.CreateDishSpec)
			authorized.PUT("/dishes/:id/specs/:spec_id", dishController.UpdateDishSpec)
			authorized.DELETE("/dishes/:id/specs/:spec_id", dishController.DeleteDishSpec)

			// 订单管理
			authorized.GET("/orders", orderController.GetAdminOrders)
			authorized.PUT("/orders/:id/status", orderController.UpdateOrderStatus)
			authorized.GET("/orders/stats", orderController.GetOrderStats)

			// 优惠券管理
			authorized.GET("/coupons", couponController.GetCoupons)
			authorized.POST("/coupons", couponController.CreateCoupon)
			authorized.PUT("/coupons/:id", couponController.UpdateCoupon)
			authorized.DELETE("/coupons/:id", couponController.DeleteCoupon)

			// 评价管理
			authorized.GET("/reviews", reviewController.GetAdminReviews)
			authorized.PUT("/reviews/:id/reply", reviewController.ReplyReview)
			authorized.PUT("/reviews/:id/status", reviewController.UpdateReviewStatus)

			// 数据统计
			authorized.GET("/stats/dashboard", orderController.GetDashboardStats)
			authorized.GET("/stats/sales", orderController.GetSalesStats)
		}
	}

	// 超级管理员API
	superAdmin := v1.Group("/super-admin")
	{
		// 超级管理员认证
		superAdmin.POST("/auth/login", authController.SuperAdminLogin)

		// 需要超级管理员权限的接口
		authorized := superAdmin.Group("")
		authorized.Use(middleware.SuperAdminAuth(cfg))
		{
			// 餐厅管理
			authorized.GET("/restaurants", restaurantController.GetRestaurants)
			authorized.POST("/restaurants", restaurantController.CreateRestaurant)
			authorized.PUT("/restaurants/:id", restaurantController.UpdateRestaurantBySuperAdmin)
			authorized.DELETE("/restaurants/:id", restaurantController.DeleteRestaurant)
			authorized.PUT("/restaurants/:id/status", restaurantController.UpdateRestaurantStatus)

			// 餐厅管理员管理
			authorized.GET("/restaurants/:id/admins", restaurantController.GetRestaurantAdmins)
			authorized.POST("/restaurants/:id/admins", restaurantController.CreateRestaurantAdmin)
			authorized.PUT("/restaurants/:id/admins/:admin_id", restaurantController.UpdateRestaurantAdmin)
			authorized.DELETE("/restaurants/:id/admins/:admin_id", restaurantController.DeleteRestaurantAdmin)

			// 支付和账单管理
			authorized.GET("/payments", paymentController.GetPayments)
			authorized.GET("/payments/monthly-fees", paymentController.GetMonthlyFees)
			authorized.POST("/payments/monthly-fees", paymentController.CreateMonthlyFee)

			// 系统统计
			authorized.GET("/stats/overview", restaurantController.GetSystemStats)
			authorized.GET("/stats/revenue", paymentController.GetRevenueStats)
		}
	}

	// 公共接口（不需要认证）
	public := v1.Group("/public")
	{
		// 获取餐厅基本信息（通过二维码扫描）
		public.GET("/restaurants/:id/info", restaurantController.GetPublicRestaurantInfo)
		public.GET("/branches/:id/info", branchController.GetPublicBranchInfo)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})
}
