# 服务器配置
PORT=3000
GIN_MODE=debug

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=restaurant_miniprogram
DB_CHARSET=utf8mb4

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE_HOURS=24

# 微信小程序配置
WECHAT_APPID=your-wechat-appid
WECHAT_SECRET=your-wechat-secret

# 微信支付配置
WECHAT_PAY_MCHID=your-mch-id
WECHAT_PAY_KEY=your-pay-key
WECHAT_PAY_CERT_PATH=./certs/apiclient_cert.pem
WECHAT_PAY_KEY_PATH=./certs/apiclient_key.pem

# 阿里云OSS配置
OSS_ENDPOINT=your-oss-endpoint
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret
OSS_BUCKET_NAME=your-bucket-name

# 系统配置
UPLOAD_MAX_SIZE=10485760
REFERRAL_COMMISSION_RATE=0.05
