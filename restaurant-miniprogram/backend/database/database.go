package database

import (
	"fmt"
	"restaurant-miniprogram/config"
	"restaurant-miniprogram/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Init(cfg *config.Config) (*gorm.DB, error) {
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Name,
		cfg.Database.Charset,
	)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移
	if err := autoMigrate(db); err != nil {
		return nil, err
	}

	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.SuperAdmin{},
		&models.Restaurant{},
		&models.RestaurantAdmin{},
		&models.Branch{},
		&models.Category{},
		&models.Dish{},
		&models.DishSpec{},
		&models.WeChatUser{},
		&models.Order{},
		&models.OrderItem{},
		&models.Coupon{},
		&models.UserCoupon{},
		&models.ReferralCommission{},
		&models.Payment{},
		&models.Review{},
	)
}
