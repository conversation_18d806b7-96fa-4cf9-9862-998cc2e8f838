package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewUserController(db *gorm.DB, cfg *config.Config) *UserController {
	return &UserController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现用户相关方法
func (uc *UserController) GetProfile(c *gin.Context) {
	// 实现获取用户资料
}

func (uc *UserController) UpdateProfile(c *gin.Context) {
	// 实现更新用户资料
}

func (uc *UserController) GetOrders(c *gin.Context) {
	// 实现获取用户订单
}

func (uc *UserController) GetCoupons(c *gin.Context) {
	// 实现获取用户优惠券
}

func (uc *UserController) GetReferrals(c *gin.Context) {
	// 实现获取推荐记录
}
