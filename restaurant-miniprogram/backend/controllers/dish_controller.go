package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DishController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewDishController(db *gorm.DB, cfg *config.Config) *DishController {
	return &DishController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现菜品相关方法
func (dc *DishController) GetDish(c *gin.Context) {
	// 实现获取菜品详情
}

func (dc *DishController) GetDishes(c *gin.Context) {
	// 实现获取菜品列表
}

func (dc *DishController) CreateDish(c *gin.Context) {
	// 实现创建菜品
}

func (dc *DishController) UpdateDish(c *gin.Context) {
	// 实现更新菜品
}

func (dc *DishController) DeleteDish(c *gin.Context) {
	// 实现删除菜品
}

func (dc *DishController) CreateDishSpec(c *gin.Context) {
	// 实现创建菜品规格
}

func (dc *DishController) UpdateDishSpec(c *gin.Context) {
	// 实现更新菜品规格
}

func (dc *DishController) DeleteDishSpec(c *gin.Context) {
	// 实现删除菜品规格
}
