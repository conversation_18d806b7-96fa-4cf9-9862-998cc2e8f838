package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CategoryController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewCategoryController(db *gorm.DB, cfg *config.Config) *CategoryController {
	return &CategoryController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现菜品分类相关方法
func (cc *CategoryController) GetCategories(c *gin.Context) {
	// 实现获取分类列表
}

func (cc *CategoryController) CreateCategory(c *gin.Context) {
	// 实现创建分类
}

func (cc *CategoryController) UpdateCategory(c *gin.Context) {
	// 实现更新分类
}

func (cc *CategoryController) DeleteCategory(c *gin.Context) {
	// 实现删除分类
}
