package controllers

import (
	"net/http"
	"restaurant-miniprogram/config"
	"restaurant-miniprogram/models"
	"restaurant-miniprogram/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewAuthController(db *gorm.DB, cfg *config.Config) *AuthController {
	return &AuthController{
		db:  db,
		cfg: cfg,
	}
}

// 微信登录请求结构
type WeChatLoginRequest struct {
	Code string `json:"code" binding:"required"`
}

// 微信登录响应结构
type WeChatLoginResponse struct {
	Token    string              `json:"token"`
	User     *models.WeChatUser  `json:"user"`
	IsNewUser bool               `json:"is_new_user"`
}

// 微信登录
func (ac *AuthController) WeChatLogin(c *gin.Context) {
	var req WeChatLoginRequest
	if err := c.<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 调用微信API获取用户信息
	// 这里先模拟一个openid
	openid := "mock_openid_" + req.Code

	// 查找或创建用户
	var user models.WeChatUser
	isNewUser := false
	
	err := ac.db.Where("openid = ?", openid).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新用户
			user = models.WeChatUser{
				OpenID:       openid,
				Status:       1,
				ReferralCode: utils.GenerateReferralCode(),
			}
			if err := ac.db.Create(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "创建用户失败",
				})
				return
			}
			isNewUser = true
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "数据库错误",
			})
			return
		}
	}

	// 生成JWT token
	token, err := utils.GenerateWeChatUserToken(user.ID, user.OpenID, ac.cfg.JWT.Secret, ac.cfg.JWT.ExpireHours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成token失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登录成功",
		"data": WeChatLoginResponse{
			Token:     token,
			User:      &user,
			IsNewUser: isNewUser,
		},
	})
}

// 微信注册（更新用户信息）
func (ac *AuthController) WeChatRegister(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未登录",
		})
		return
	}

	var req struct {
		Nickname string `json:"nickname"`
		Avatar   string `json:"avatar"`
		Gender   int8   `json:"gender"`
		Phone    string `json:"phone"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 更新用户信息
	var user models.WeChatUser
	if err := ac.db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	user.Nickname = req.Nickname
	user.Avatar = req.Avatar
	user.Gender = req.Gender
	user.Phone = req.Phone

	if err := ac.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新用户信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "注册成功",
		"data":    user,
	})
}

// 餐厅管理员登录请求结构
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// 餐厅管理员登录
func (ac *AuthController) AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 查找管理员
	var admin models.RestaurantAdmin
	err := ac.db.Preload("Restaurant").Where("username = ? AND status = 1", req.Username).First(&admin).Error
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户名或密码错误",
		})
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户名或密码错误",
		})
		return
	}

	// 检查餐厅状态
	if admin.Restaurant.Status != 1 {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "餐厅已停用",
		})
		return
	}

	// 生成JWT token
	token, err := utils.GenerateRestaurantAdminToken(admin.ID, admin.RestaurantID, ac.cfg.JWT.Secret, ac.cfg.JWT.ExpireHours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成token失败",
		})
		return
	}

	// 更新最后登录时间
	ac.db.Model(&admin).Update("last_login_at", "NOW()")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登录成功",
		"data": gin.H{
			"token": token,
			"admin": admin,
		},
	})
}

// 超级管理员登录
func (ac *AuthController) SuperAdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 查找超级管理员
	var admin models.SuperAdmin
	err := ac.db.Where("username = ? AND status = 1", req.Username).First(&admin).Error
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户名或密码错误",
		})
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户名或密码错误",
		})
		return
	}

	// 生成JWT token
	token, err := utils.GenerateSuperAdminToken(admin.ID, ac.cfg.JWT.Secret, ac.cfg.JWT.ExpireHours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成token失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登录成功",
		"data": gin.H{
			"token": token,
			"admin": admin,
		},
	})
}
