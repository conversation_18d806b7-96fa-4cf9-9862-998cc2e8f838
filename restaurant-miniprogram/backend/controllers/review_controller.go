package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ReviewController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewReviewController(db *gorm.DB, cfg *config.Config) *ReviewController {
	return &ReviewController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现评价相关方法
func (rc *ReviewController) CreateReview(c *gin.Context) {
	// 实现创建评价
}

func (rc *ReviewController) GetReviews(c *gin.Context) {
	// 实现获取评价列表
}

func (rc *ReviewController) GetAdminReviews(c *gin.Context) {
	// 实现获取管理员评价列表
}

func (rc *ReviewController) ReplyReview(c *gin.Context) {
	// 实现回复评价
}

func (rc *ReviewController) UpdateReviewStatus(c *gin.Context) {
	// 实现更新评价状态
}
