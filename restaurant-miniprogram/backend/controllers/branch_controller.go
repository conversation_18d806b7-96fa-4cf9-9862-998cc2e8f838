package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BranchController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewBranchController(db *gorm.DB, cfg *config.Config) *BranchController {
	return &BranchController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现分店相关方法
func (bc *BranchController) GetBranch(c *gin.Context) {
	// 实现获取分店信息
}

func (bc *BranchController) GetPublicBranchInfo(c *gin.Context) {
	// 实现获取公开分店信息
}

func (bc *BranchController) GetMenu(c *gin.Context) {
	// 实现获取分店菜单
}

func (bc *BranchController) GetBranches(c *gin.Context) {
	// 实现获取分店列表
}

func (bc *BranchController) CreateBranch(c *gin.Context) {
	// 实现创建分店
}

func (bc *BranchController) UpdateBranch(c *gin.Context) {
	// 实现更新分店
}

func (bc *BranchController) DeleteBranch(c *gin.Context) {
	// 实现删除分店
}

func (bc *BranchController) GenerateQRCode(c *gin.Context) {
	// 实现生成二维码
}
