package controllers

import (
	"net/http"
	"restaurant-miniprogram/config"
	"restaurant-miniprogram/models"
	"restaurant-miniprogram/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type RestaurantController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewRestaurantController(db *gorm.DB, cfg *config.Config) *RestaurantController {
	return &RestaurantController{
		db:  db,
		cfg: cfg,
	}
}

// 获取餐厅信息（小程序用）
func (rc *RestaurantController) GetRestaurant(c *gin.Context) {
	id := c.Param("id")
	restaurantID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "餐厅ID格式错误",
		})
		return
	}

	var restaurant models.Restaurant
	err = rc.db.Preload("Branches", "status = 1").
		Preload("Categories", "status = 1").
		Where("id = ? AND status = 1", restaurantID).
		First(&restaurant).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "餐厅不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "数据库错误",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    restaurant,
	})
}

// 获取公开餐厅信息（通过二维码扫描）
func (rc *RestaurantController) GetPublicRestaurantInfo(c *gin.Context) {
	id := c.Param("id")
	restaurantID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "餐厅ID格式错误",
		})
		return
	}

	var restaurant models.Restaurant
	err = rc.db.Select("id, name, logo, description").
		Where("id = ? AND status = 1", restaurantID).
		First(&restaurant).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "餐厅不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "数据库错误",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    restaurant,
	})
}

// 获取管理员的餐厅信息
func (rc *RestaurantController) GetAdminRestaurant(c *gin.Context) {
	restaurantID, exists := c.Get("restaurant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	var restaurant models.Restaurant
	err := rc.db.Preload("Branches").
		Preload("Categories").
		Where("id = ?", restaurantID).
		First(&restaurant).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "数据库错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    restaurant,
	})
}

// 更新餐厅信息
func (rc *RestaurantController) UpdateRestaurant(c *gin.Context) {
	restaurantID, exists := c.Get("restaurant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	var req struct {
		Name        string `json:"name"`
		Logo        string `json:"logo"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	var restaurant models.Restaurant
	if err := rc.db.First(&restaurant, restaurantID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "餐厅不存在",
		})
		return
	}

	// 更新字段
	if req.Name != "" {
		restaurant.Name = req.Name
	}
	if req.Logo != "" {
		restaurant.Logo = req.Logo
	}
	if req.Description != "" {
		restaurant.Description = req.Description
	}

	if err := rc.db.Save(&restaurant).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    restaurant,
	})
}

// 超级管理员获取所有餐厅
func (rc *RestaurantController) GetRestaurants(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status := c.Query("status")
	keyword := c.Query("keyword")

	offset := utils.CalculateOffset(page, pageSize)

	query := rc.db.Model(&models.Restaurant{})

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR contact_person LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 获取数据
	var restaurants []models.Restaurant
	err := query.Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&restaurants).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "数据库错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    utils.NewPaginationResponse(page, pageSize, total, restaurants),
	})
}

// 超级管理员创建餐厅
func (rc *RestaurantController) CreateRestaurant(c *gin.Context) {
	var req struct {
		Name             string  `json:"name" binding:"required"`
		ContactPerson    string  `json:"contact_person" binding:"required"`
		ContactPhone     string  `json:"contact_phone" binding:"required"`
		ContactEmail     string  `json:"contact_email"`
		BusinessLicense  string  `json:"business_license"`
		SubscriptionPlan string  `json:"subscription_plan"`
		MonthlyFee       float64 `json:"monthly_fee"`
		MaxBranches      int     `json:"max_branches"`
		
		// 管理员信息
		AdminUsername string `json:"admin_username" binding:"required"`
		AdminPassword string `json:"admin_password" binding:"required"`
		AdminName     string `json:"admin_name" binding:"required"`
		AdminPhone    string `json:"admin_phone" binding:"required"`
		AdminEmail    string `json:"admin_email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 开始事务
	tx := rc.db.Begin()

	// 创建餐厅
	restaurant := models.Restaurant{
		Name:             req.Name,
		ContactPerson:    req.ContactPerson,
		ContactPhone:     req.ContactPhone,
		ContactEmail:     req.ContactEmail,
		BusinessLicense:  req.BusinessLicense,
		SubscriptionPlan: req.SubscriptionPlan,
		MonthlyFee:       req.MonthlyFee,
		MaxBranches:      req.MaxBranches,
		Status:           1,
	}

	if err := tx.Create(&restaurant).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建餐厅失败",
		})
		return
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.AdminPassword), bcrypt.DefaultCost)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "密码加密失败",
		})
		return
	}

	// 创建管理员
	admin := models.RestaurantAdmin{
		RestaurantID: restaurant.ID,
		Username:     req.AdminUsername,
		Password:     string(hashedPassword),
		Name:         req.AdminName,
		Phone:        req.AdminPhone,
		Email:        req.AdminEmail,
		Role:         "admin",
		Status:       1,
	}

	if err := tx.Create(&admin).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建管理员失败",
		})
		return
	}

	// 提交事务
	tx.Commit()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data": gin.H{
			"restaurant": restaurant,
			"admin":      admin,
		},
	})
}
