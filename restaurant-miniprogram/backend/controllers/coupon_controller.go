package controllers

import (
	"restaurant-miniprogram/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CouponController struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewCouponController(db *gorm.DB, cfg *config.Config) *CouponController {
	return &CouponController{
		db:  db,
		cfg: cfg,
	}
}

// TODO: 实现优惠券相关方法
func (cc *CouponController) ClaimCoupon(c *gin.Context) {
	// 实现领取优惠券
}

func (cc *CouponController) GetCoupons(c *gin.Context) {
	// 实现获取优惠券列表
}

func (cc *CouponController) CreateCoupon(c *gin.Context) {
	// 实现创建优惠券
}

func (cc *CouponController) UpdateCoupon(c *gin.Context) {
	// 实现更新优惠券
}

func (cc *CouponController) DeleteCoupon(c *gin.Context) {
	// 实现删除优惠券
}
