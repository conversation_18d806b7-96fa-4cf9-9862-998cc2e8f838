-- 餐厅微信小程序数据库设计
-- 支持多租户SaaS架构

-- 1. 超级管理员表
CREATE TABLE `super_admins` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL UNIQUE,
  `phone` varchar(20),
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='超级管理员表';

-- 2. 餐厅公司表
CREATE TABLE `restaurants` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '餐厅名称',
  `logo` varchar(255) COMMENT '餐厅logo',
  `description` text COMMENT '餐厅描述',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `business_license` varchar(100) COMMENT '营业执照号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:正常 0:停用 2:欠费',
  `subscription_plan` varchar(50) NOT NULL DEFAULT 'basic' COMMENT '订阅计划',
  `monthly_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '月费',
  `max_branches` int(11) NOT NULL DEFAULT 1 COMMENT '最大分店数',
  `features` json COMMENT '功能权限配置',
  `expired_at` timestamp NULL COMMENT '到期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_expired_at` (`expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='餐厅公司表';

-- 3. 餐厅管理员表
CREATE TABLE `restaurant_admins` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) NOT NULL,
  `email` varchar(100),
  `role` varchar(20) NOT NULL DEFAULT 'admin' COMMENT 'admin:管理员 staff:员工',
  `permissions` json COMMENT '权限配置',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `last_login_at` timestamp NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_restaurant_username` (`restaurant_id`, `username`),
  KEY `idx_restaurant_id` (`restaurant_id`),
  CONSTRAINT `fk_restaurant_admins_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='餐厅管理员表';

-- 4. 分店表
CREATE TABLE `branches` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '分店名称',
  `address` varchar(255) NOT NULL COMMENT '分店地址',
  `phone` varchar(20) COMMENT '分店电话',
  `latitude` decimal(10,7) COMMENT '纬度',
  `longitude` decimal(10,7) COMMENT '经度',
  `business_hours` json COMMENT '营业时间',
  `qr_code` varchar(255) COMMENT '二维码图片路径',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:营业 0:停业',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_restaurant_id` (`restaurant_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_branches_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分店表';

-- 5. 菜品分类表
CREATE TABLE `categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) COMMENT '分类描述',
  `image` varchar(255) COMMENT '分类图片',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_restaurant_id` (`restaurant_id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  CONSTRAINT `fk_categories_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品分类表';

-- 6. 菜品表
CREATE TABLE `dishes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `category_id` bigint(20) unsigned NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '菜品名称',
  `description` text COMMENT '菜品描述',
  `image` varchar(255) COMMENT '菜品图片',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) COMMENT '原价',
  `unit` varchar(20) DEFAULT '份' COMMENT '单位',
  `stock` int(11) DEFAULT -1 COMMENT '库存，-1表示无限',
  `sales_count` int(11) NOT NULL DEFAULT 0 COMMENT '销量',
  `rating` decimal(3,2) DEFAULT 5.00 COMMENT '评分',
  `is_recommended` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `is_spicy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否辣',
  `tags` json COMMENT '标签',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:上架 0:下架',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_restaurant_category` (`restaurant_id`, `category_id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_recommended` (`is_recommended`),
  CONSTRAINT `fk_dishes_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_dishes_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品表';

-- 7. 菜品规格表
CREATE TABLE `dish_specs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `dish_id` bigint(20) unsigned NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '规格名称，如：大杯、中杯、小杯',
  `price_diff` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格差异',
  `stock` int(11) DEFAULT -1 COMMENT '库存',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_dish_id` (`dish_id`),
  CONSTRAINT `fk_dish_specs_dish` FOREIGN KEY (`dish_id`) REFERENCES `dishes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品规格表';

-- 8. 微信用户表
CREATE TABLE `wechat_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL UNIQUE COMMENT '微信openid',
  `unionid` varchar(100) COMMENT '微信unionid',
  `nickname` varchar(100) COMMENT '昵称',
  `avatar` varchar(255) COMMENT '头像',
  `gender` tinyint(1) COMMENT '性别 1:男 2:女 0:未知',
  `phone` varchar(20) COMMENT '手机号',
  `birthday` date COMMENT '生日',
  `points` int(11) NOT NULL DEFAULT 0 COMMENT '积分',
  `total_spent` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总消费金额',
  `referrer_id` bigint(20) unsigned COMMENT '推荐人ID',
  `referral_code` varchar(20) UNIQUE COMMENT '推荐码',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:正常 0:禁用',
  `last_login_at` timestamp NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referral_code` (`referral_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';

-- 9. 订单表
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) NOT NULL UNIQUE COMMENT '订单号',
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `branch_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `table_number` varchar(20) COMMENT '桌号',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '支付方式',
  `payment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:待支付 1:已支付 2:已退款',
  `order_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:待确认 1:已确认 2:制作中 3:待取餐 4:已完成 5:已取消',
  `order_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:堂食 2:外带 3:外卖',
  `pickup_number` varchar(10) COMMENT '取餐号',
  `estimated_time` int(11) COMMENT '预计完成时间(分钟)',
  `remark` text COMMENT '备注',
  `referrer_id` bigint(20) unsigned COMMENT '推荐人ID',
  `commission_amount` decimal(10,2) DEFAULT 0.00 COMMENT '返佣金额',
  `paid_at` timestamp NULL COMMENT '支付时间',
  `completed_at` timestamp NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_restaurant_branch` (`restaurant_id`, `branch_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_orders_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`),
  CONSTRAINT `fk_orders_branch` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`),
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `wechat_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 10. 订单详情表
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `dish_id` bigint(20) unsigned NOT NULL,
  `dish_spec_id` bigint(20) unsigned COMMENT '菜品规格ID',
  `dish_name` varchar(100) NOT NULL COMMENT '菜品名称快照',
  `dish_spec_name` varchar(50) COMMENT '规格名称快照',
  `price` decimal(10,2) NOT NULL COMMENT '单价',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '小计',
  `remark` varchar(255) COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_dish_id` (`dish_id`),
  CONSTRAINT `fk_order_items_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_order_items_dish` FOREIGN KEY (`dish_id`) REFERENCES `dishes` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单详情表';

-- 11. 优惠券表
CREATE TABLE `coupons` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `type` tinyint(1) NOT NULL COMMENT '1:满减 2:折扣 3:免费菜品',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) DEFAULT 0.00 COMMENT '最低消费金额',
  `max_discount` decimal(10,2) COMMENT '最大优惠金额',
  `total_quantity` int(11) NOT NULL COMMENT '总发放数量',
  `used_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `per_user_limit` int(11) DEFAULT 1 COMMENT '每人限领数量',
  `valid_from` timestamp NOT NULL COMMENT '有效期开始',
  `valid_to` timestamp NOT NULL COMMENT '有效期结束',
  `applicable_branches` json COMMENT '适用分店ID数组',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_restaurant_id` (`restaurant_id`),
  KEY `idx_status_valid` (`status`, `valid_from`, `valid_to`),
  CONSTRAINT `fk_coupons_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 12. 用户优惠券表
CREATE TABLE `user_coupons` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `coupon_id` bigint(20) unsigned NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:未使用 1:已使用 2:已过期',
  `used_order_id` bigint(20) unsigned COMMENT '使用的订单ID',
  `used_at` timestamp NULL COMMENT '使用时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_user_coupons_user` FOREIGN KEY (`user_id`) REFERENCES `wechat_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_coupons_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 13. 推荐返佣记录表
CREATE TABLE `referral_commissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `referrer_id` bigint(20) unsigned NOT NULL COMMENT '推荐人ID',
  `referee_id` bigint(20) unsigned NOT NULL COMMENT '被推荐人ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `commission_rate` decimal(5,4) NOT NULL COMMENT '返佣比例',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '返佣金额',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:待结算 1:已结算 2:已取消',
  `settled_at` timestamp NULL COMMENT '结算时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referee_id` (`referee_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_referral_commissions_referrer` FOREIGN KEY (`referrer_id`) REFERENCES `wechat_users` (`id`),
  CONSTRAINT `fk_referral_commissions_referee` FOREIGN KEY (`referee_id`) REFERENCES `wechat_users` (`id`),
  CONSTRAINT `fk_referral_commissions_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐返佣记录表';

-- 14. 支付记录表
CREATE TABLE `payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `payment_no` varchar(32) NOT NULL UNIQUE COMMENT '支付单号',
  `order_id` bigint(20) unsigned COMMENT '订单ID',
  `restaurant_id` bigint(20) unsigned COMMENT '餐厅ID(用于月费支付)',
  `user_id` bigint(20) unsigned COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `payment_type` tinyint(1) NOT NULL COMMENT '1:订单支付 2:月费支付',
  `payment_method` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '支付方式',
  `third_party_no` varchar(100) COMMENT '第三方支付单号',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:待支付 1:已支付 2:已退款 3:支付失败',
  `paid_at` timestamp NULL COMMENT '支付时间',
  `refunded_at` timestamp NULL COMMENT '退款时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_restaurant_id` (`restaurant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_payments_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `fk_payments_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`),
  CONSTRAINT `fk_payments_user` FOREIGN KEY (`user_id`) REFERENCES `wechat_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 15. 评价表
CREATE TABLE `reviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `restaurant_id` bigint(20) unsigned NOT NULL,
  `branch_id` bigint(20) unsigned NOT NULL,
  `rating` tinyint(1) NOT NULL COMMENT '评分 1-5',
  `content` text COMMENT '评价内容',
  `images` json COMMENT '评价图片',
  `reply` text COMMENT '商家回复',
  `replied_at` timestamp NULL COMMENT '回复时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1:显示 0:隐藏',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_restaurant_branch` (`restaurant_id`, `branch_id`),
  KEY `idx_status_created` (`status`, `created_at`),
  CONSTRAINT `fk_reviews_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `fk_reviews_user` FOREIGN KEY (`user_id`) REFERENCES `wechat_users` (`id`),
  CONSTRAINT `fk_reviews_restaurant` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants` (`id`),
  CONSTRAINT `fk_reviews_branch` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价表';
