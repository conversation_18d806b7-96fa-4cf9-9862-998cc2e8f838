# 餐厅微信小程序系统

一个支持多租户的餐厅微信小程序SaaS平台，包含小程序前端、餐厅管理后台和超级管理员系统。

## 系统架构

```
├── backend/                 # Go后端API
│   ├── config/             # 配置管理
│   ├── controllers/        # 控制器
│   ├── database/           # 数据库连接
│   ├── middleware/         # 中间件
│   ├── models/             # 数据模型
│   ├── routes/             # 路由配置
│   ├── utils/              # 工具函数
│   ├── main.go             # 入口文件
│   ├── go.mod              # Go模块文件
│   └── Dockerfile          # Docker构建文件
├── frontend/               # 前端文件
│   ├── miniprogram/        # 微信小程序
│   ├── admin/              # 餐厅管理后台
│   └── super-admin/        # 超级管理员后台
├── database/               # 数据库文件
│   └── schema.sql          # 数据库结构
├── nginx/                  # Nginx配置
├── docker-compose.yml      # Docker编排文件
└── README.md               # 项目说明
```

## 核心功能

### 微信小程序功能
- 🔐 微信授权登录
- 🏪 扫码进入餐厅/分店
- 📱 浏览菜单和菜品详情
- 🛒 点餐购物车功能
- 💰 微信支付下单
- 📤 分享餐厅/菜品给好友
- 📋 查看订单历史
- ⭐ 评价和反馈
- 🎫 优惠券领取和使用
- 💸 推荐返佣功能

### 餐厅管理后台功能
- 🏢 餐厅信息管理
- 🏪 多分店管理
- 🍽️ 菜品管理（增删改查、分类、价格、规格）
- 📦 订单管理和状态更新
- 📊 营业数据统计
- 🔗 二维码生成（每个分店独立）
- 🎫 优惠券管理
- 💬 评价管理和回复
- 👥 员工权限管理

### 超级管理员后台功能
- 🏢 餐厅账户管理
- 💰 月费收费管理
- 📈 系统使用统计
- 📊 平台数据分析
- 🛠️ 技术支持管理
- 💳 支付和账单管理

## 技术栈

### 后端
- **语言**: Go 1.21
- **框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **认证**: JWT
- **支付**: 微信支付API
- **文件存储**: 阿里云OSS

### 前端
- **小程序**: 微信小程序原生开发
- **管理后台**: Bootstrap + HTML/CSS/JS
- **UI组件**: Bootstrap 5

### 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **云服务**: 阿里云

## 数据库设计

### 核心表结构
- `super_admins` - 超级管理员表
- `restaurants` - 餐厅公司表
- `restaurant_admins` - 餐厅管理员表
- `branches` - 分店表
- `categories` - 菜品分类表
- `dishes` - 菜品表
- `dish_specs` - 菜品规格表
- `wechat_users` - 微信用户表
- `orders` - 订单表
- `order_items` - 订单详情表
- `coupons` - 优惠券表
- `user_coupons` - 用户优惠券表
- `referral_commissions` - 推荐返佣记录表
- `payments` - 支付记录表
- `reviews` - 评价表

## API接口

### 微信小程序API (`/api/v1/miniprogram`)
- `POST /auth/login` - 微信登录
- `GET /user/profile` - 获取用户资料
- `GET /restaurants/:id` - 获取餐厅信息
- `GET /branches/:id/menu` - 获取分店菜单
- `POST /orders` - 创建订单
- `POST /payments/wechat` - 微信支付
- `POST /coupons/:id/claim` - 领取优惠券

### 餐厅管理后台API (`/api/v1/admin`)
- `POST /auth/login` - 管理员登录
- `GET /restaurant` - 获取餐厅信息
- `GET /branches` - 获取分店列表
- `POST /dishes` - 创建菜品
- `GET /orders` - 获取订单列表
- `GET /stats/dashboard` - 获取仪表板统计

### 超级管理员API (`/api/v1/super-admin`)
- `POST /auth/login` - 超级管理员登录
- `GET /restaurants` - 获取所有餐厅
- `POST /restaurants` - 创建餐厅
- `GET /payments/monthly-fees` - 获取月费记录
- `GET /stats/overview` - 获取系统统计

## 快速开始

### 环境要求
- Docker & Docker Compose
- Go 1.21+ (开发环境)
- MySQL 8.0+
- Redis 7+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd restaurant-miniprogram
```

2. **配置环境变量**
```bash
cd backend
cp .env.example .env
# 编辑.env文件，配置数据库、微信等信息
```

3. **启动服务**
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者开发环境启动
cd backend
go mod tidy
go run main.go
```

4. **初始化数据**
```bash
# 数据库会自动创建表结构
# 可以手动插入一些测试数据
```

### 开发环境

1. **后端开发**
```bash
cd backend
go mod tidy
go run main.go
```

2. **前端开发**
- 微信小程序：使用微信开发者工具打开 `frontend/miniprogram`
- 管理后台：直接在浏览器中打开 `frontend/admin/index.html`

## 配置说明

### 环境变量配置
详见 `backend/.env.example` 文件，主要配置项：
- 数据库连接信息
- Redis连接信息
- JWT密钥
- 微信小程序配置
- 微信支付配置
- 阿里云OSS配置

### 微信小程序配置
1. 在微信公众平台注册小程序
2. 获取AppID和AppSecret
3. 配置服务器域名
4. 配置支付商户号

### 支付配置
1. 开通微信支付
2. 下载支付证书
3. 配置支付回调地址

## 部署说明

### Docker部署
```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d
```

### 传统部署
1. 编译Go程序
2. 配置Nginx
3. 配置MySQL和Redis
4. 启动服务

## 开发计划

- [x] 数据库设计
- [x] 后端API框架搭建
- [ ] 微信小程序开发
- [ ] 餐厅管理后台开发
- [ ] 超级管理员后台开发
- [ ] 支付系统集成
- [ ] 部署和测试

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
